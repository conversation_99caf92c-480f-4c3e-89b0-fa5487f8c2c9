import { MaterialIcons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { formatPlayerValue } from '../utils/PlayerUtils';
import { logger } from '../utils/logger';

interface StyledProps {
  theme: DefaultTheme;
}

interface Transaction {
  id: string;
  gameworldId: string;
  team: string;
  date: number;
  amount: number;
  type: string;
  details: string;
}

interface TransactionsResponse {
  transactions: Transaction[];
}

const Container = styled.View<StyledProps>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background.primary};
  padding: 16px;
`;

const BalanceSection = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  align-items: center;
  border: 1px solid ${(props) => props.theme.colors.border};
`;

const BalanceLabel = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  margin-bottom: 8px;
`;

const BalanceAmount = styled(Text)<StyledProps>`
  font-size: 32px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

const SectionTitle = styled(Text)<StyledProps>`
  font-size: 20px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  margin-bottom: 16px;
`;

const TransactionCard = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid ${(props) => props.theme.colors.border};
`;

const TransactionHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const TransactionType = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  flex: 1;
`;

const TransactionAmount = styled(Text)<{ isPositive: boolean } & StyledProps>`
  font-size: 16px;
  color: ${(props) => (props.isPositive ? '#2E7D32' : '#e3172a')};
  font-family: 'NunitoBold';
`;

const TransactionDate = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

const ErrorContainer = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  align-items: center;
  border: 1px solid #e3172a;
`;

const ErrorText = styled(Text)`
  color: #e3172a;
  font-family: 'Nunito';
  text-align: center;
`;

const EmptyContainer = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.background.secondary};
  border-radius: 8px;
  padding: 20px;
  align-items: center;
  border: 1px solid ${(props) => props.theme.colors.border};
`;

const EmptyText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: center;
`;

// Utility function to format transaction types
const formatTransactionType = (type: string): string => {
  const typeMap: Record<string, string> = {
    GROUND_MAINTENANCE: 'Ground Maintenance',
    DRINKS_INCOME: 'Drinks Income',
    MERCHANDISE_INCOME: 'Merchandise Income',
    PLAYER_WAGES: 'Player Wages',
    TICKET_INCOME: 'Ticket Income',
    PROGRAMME_INCOME: 'Programme Income',
    FOOD_INCOME: 'Food Income',
    transfer: 'Transfer',
  };

  return typeMap[type] || type.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
};

// Utility function to format dates (date only for headers)
const formatTransactionDate = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-GB', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  });
};

// Utility function to get date key for grouping
const getDateKey = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

const FinancesScreen: React.FC = () => {
  const { team, manager, loading: managerLoading } = useManager();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTransactions = async (isRefresh = false) => {
    if (!manager?.gameworldId || !team?.teamId) {
      logger.log('Missing gameworldId or teamId for transactions fetch');
      return;
    }

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const response: TransactionsResponse = await callApi(
        `/${manager.gameworldId}/team/${team.teamId}/transactions?days=14`
      );

      setTransactions(response.transactions || []);
    } catch (err) {
      logger.error('Error fetching transactions:', err);
      setError('Failed to load transactions. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (!managerLoading && manager?.gameworldId && team?.teamId) {
      fetchTransactions();
    }
  }, [manager?.gameworldId, team?.teamId, managerLoading]);

  const onRefresh = () => {
    fetchTransactions(true);
  };

  const renderTransaction = ({ item }: { item: Transaction }) => (
    <TransactionCard>
      <TransactionHeader>
        <TransactionType>{formatTransactionType(item.type)}</TransactionType>
        <TransactionAmount isPositive={item.amount > 0}>
          {item.amount > 0 ? '+' : ''}
          {formatPlayerValue(item.amount)}
        </TransactionAmount>
      </TransactionHeader>
      <TransactionDate>{formatTransactionDate(item.date)}</TransactionDate>
    </TransactionCard>
  );

  if (managerLoading || (loading && !refreshing)) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  return (
    <Container>
      <BalanceSection>
        <BalanceLabel>Current Balance</BalanceLabel>
        <BalanceAmount>{formatPlayerValue(team?.balance ?? 0)}</BalanceAmount>
      </BalanceSection>

      <SectionTitle>Recent Transactions (Last 14 Days)</SectionTitle>

      {error && (
        <ErrorContainer>
          <MaterialIcons name="error" size={24} color="#e3172a" />
          <ErrorText>{error}</ErrorText>
        </ErrorContainer>
      )}

      {!error && transactions.length === 0 && !loading && (
        <EmptyContainer>
          <MaterialIcons name="receipt" size={48} color="#999" />
          <EmptyText>No transactions found for the last 14 days</EmptyText>
        </EmptyContainer>
      )}

      {!error && transactions.length > 0 && (
        <FlatList
          data={transactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        />
      )}
    </Container>
  );
};

export default FinancesScreen;
